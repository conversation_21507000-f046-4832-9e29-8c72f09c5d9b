package sg.activities.model
{
    import sg.model.ViewModelBase;
    import sg.cfg.ConfigServer;
    import sg.manager.ModelManager;
    import sg.utils.ObjectUtil;
    import sg.utils.Tools;
    import sg.model.ModelUser;

    /**
     * 永久累计充值模型 - 完全复刻累计充值功能，但永久有效且显示代金券
     */
    public class ModelPermanentPayTotal extends ViewModelBase
    {
        // 单例
        private static var sModel:ModelPermanentPayTotal = null;
        
        public static function get instance():ModelPermanentPayTotal {
            return sModel ||= new ModelPermanentPayTotal();
        }
        
        private var cfg:Object;
        private var permanentPayData:Object;
        
        public function ModelPermanentPayTotal() {
            // 获取配置
            this.cfg = ConfigServer.ploy['permanent_total_pay'];
            haveConfig = Boolean(cfg);
        }

        override public function refreshData(data:*):void {
            if (!haveConfig) return;
            this.permanentPayData = data;
            this.event(ModelActivities.UPDATE_DATA);
        }

        override public function get active():Boolean {
            if (!haveConfig) return false;
            // 永久累计充值始终激活（如果配置存在）
            return ModelManager.instance.modelUser.canPay;
        }

        override public function get redPoint():Boolean {
            if (!haveConfig || !permanentPayData) return false;
            
            var totalPayMoney:int = this.getTotalPayMoney();
            var rewardStatus:Object = this.getRewardStatus();
            
            // 检查是否有可领取的奖励
            var rewardKeys:Array = ObjectUtil.keys(cfg.reward);
            for (var i:int = 0; i < rewardKeys.length; i++) {
                var rewardMoney:int = parseInt(rewardKeys[i]);
                if (totalPayMoney >= rewardMoney && rewardStatus[rewardKeys[i]] != 1) {
                    return true;
                }
            }
            return false;
        }

        /**
         * 获取总充值金额（人民币）
         */
        public function getTotalPayMoney():int {
            return permanentPayData ? (permanentPayData.total_pay_money || 0) : 0;
        }

        /**
         * 获取奖励状态
         */
        public function getRewardStatus():Object {
            return permanentPayData ? (permanentPayData.permanent_pay_rewards || {}) : {};
        }

        /**
         * 检查红点（类似PayTotal的checkRewardByType）
         */
        public function checkReward():Boolean {
            if (!cfg) return false;
            var data:Object = this.getData();
            if (!data) return false;
            
            var pay_list:Array = data['pay_list'];
            var reward_list:Array = data['reward_list'];
            var totalPay:int = 0;
            var len:int = pay_list.length;
            for(var i:int = 0; i < len; i++) {
                totalPay += pay_list[i];
            }
            var keys:Array = ObjectUtil.keys(cfg['reward']);
            for(i = 0, len = keys.length; i < len; i++){
                var needMoney:int = parseInt(keys[i]);
                if (totalPay >= needMoney && reward_list.indexOf(needMoney) === -1) {
                    return true;
                }
            }
            return false;
        }

        /**
         * 获取配置数据（类似PayTotal的getConfigByID）
         */
        public function getConfig():Object {
            return cfg;
        }

        /**
         * 获取数据（类似PayTotal的getDataByID）
         */
        public function getData():Object {
            if (!permanentPayData) return null;
            
            // 模拟PayTotal的数据结构
            var pay_list:Array = [this.getTotalPayMoney()]; // 总充值金额
            var reward_list:Array = [];
            var rewardStatus:Object = this.getRewardStatus();
            
            // 构建已领取奖励列表
            var rewardKeys:Array = ObjectUtil.keys(rewardStatus);
            for (var i:int = 0; i < rewardKeys.length; i++) {
                if (rewardStatus[rewardKeys[i]] == 1) {
                    reward_list.push(parseInt(rewardKeys[i]));
                }
            }
            
            return {
                pay_list: pay_list,
                reward_list: reward_list
            };
        }

        /**
         * 获取剩余时间（永久活动返回很大的数值）
         */
        public function getRemainingTime():int {
            return 999999999; // 永久活动
        }

        /**
         * 获取时间字符串
         */
        public function getTimeString():String {
            return "永久有效";
        }
    }
}
