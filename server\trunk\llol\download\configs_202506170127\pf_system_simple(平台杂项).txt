{
        'server_port': [
        10000, 
                [
                        [100, '118.178.139.38'],     #（服务器配置，其他人不要动）跨服战服务器个数，服务器地址
                ],
                [
                        ['118.178.139.38',9901],     #（服务器配置，其他人不要动）跨服战管理服务器,每个管理400个战场
                ],

        ],
	'online_is': 1,   #为1时检查外挂，每30秒发送online_is收集（仅用于sg3后台！）
        'update_app':{#更新app安装包的页面
            #'test':'http://cdn2.ptkill.com/static/zqwz2.apk',
        },

        'inputEnabled':1,#是否开启聊天点一下之后弹出的输入框 0屏蔽1打开
'act_url':{
                 'ios_37':['https://hdapi.37.com/?c=api&a=login_page&alias_info=zqwzgameicon20200221&f=d202002/zqwzgameicon20200221&',0],
               },
'act2_url':{
                 'ios_37':[' https://www.wjx.cn/vm/hxTAoeL.aspx?',0],
                 'h5_37':['https://www.wjx.cn/vm/hxTAoeL.aspx?',0],
               },
        'pay_alert_pf':[],#弹出充值警告的pf(37日语用的)

        'act_addbase':0,            #0为累积充值RMB，1为累积充值coin.
########################################
        'input_lang_default':['cn'],#默认输入语言限制 cn 简体中文 en 英文 过渡期间保留zh，后期统一使用cn
        'input_lang_pf':{'r2g_xm_ad':['cn','en'],'r2g_xm_ios':['cn','en'],'r2g_xm':['cn','en'],'r2g_kr_os':['cn','en']},#分平台输入语言限制，取不到取default
        'name_limit':{ # 取名限制(允许输入的字符、单个字符所占长度, 允许的最大字符数)
                'en': ['a-zA-Z ·*=+-_', 1, 13],
                'cn': ['\u4e00-\u9fa5', 2, 5],
                'kr': ['', 1, 10],
                'ar': ['', 1, 50]
        },
        'info_font_size':[11,12,13,14,15,  16,17,18,20,22],     #大面积文字（天赋）使用的字号0~9设置
        'info_font_size_lan':{   #针对于不同的语言，大面积文字的字号系数
           'en':0.8,
           #'cn':0.5,
        },  


###########
        'pay_active':1,#游戏中是否显示支付入口（用于屏蔽支付功能）
        'pay_self_lv':1,#自己支付的等级限制
        'pay_self_pf':['developer','test','ios_mjm1','ios_mjm2','ios_mjm5','ios_mjm6','dev','ios_mjm3','ios_mj1','ios_mjm4','test_sgqx','h5_yyjh2','yyb','h5_leyou',''],#自己的支付平台
        'pay_self_h5_pf':['developer','ios_mjm1','ios_mjm2','ios_mjm5','ios_mjm6','dev','ios_mjm3','ios_mj1','ios_mjm4','test_sgqx','h5_yyjh2','yyb','test','h5_leyou',''],#自己的h5支付平台
        'pay_warning_pf':['cn_gdzj'],#不能支付平台提醒
###################################
        'wx_pay_test':0,#微信测试支付模式 == 1 是 ==0 不是(正式环境)
        'wx_share':{'title':'_lht55','imageUrl':'wx_share_ad2.jpg'},#微信分享配置
        'qq_share':{'title':'_lht55','imageUrl':'wx_share_ad1.jpg','des':'_lht55','link':'http://www.ptkill.com/'},#QQ分享配置
                                
                                 #['title','desc','icon','_clint_link','server_link']
        'share_pf':{  #[标题,描述,广告图,链接,备用,微信qq都有]
                      'developer':['_share_text01','_share_text01','wx_share_ad.png','www.baidu.com','http://sg.ptkill.com/static/h5/wx_share/lhw_index.html',0], #本地开发
                      'wx'       :['title','desc','icon','link','www.baidu.com',0],#微信(无须配置 依照系统杂项中的wx_share)
                      'h5_qq'       :['title','desc','icon','link','www.baidu.com',0],#QQ(无须配置 依照系统杂项中的wx_share)
                      'test'    :['_lht55','_lht55','http://cdn2.ptkill.com/static/h5/meng52/ad/wx_share_ad2.jpg','https://sg.ptkill.com/www1/','https://sg.ptkill.com/www1/',0],#官网分享正式配置
                   },
        'wx_login':1,#微信登录是否启用
###################################
        'phone_pf':['test','ios','ios_mjm1','ios_mj1','ios_mjm2','ios_mjm4','ios_mjm5','ios_mjm6','ios_sh','ios_djcy','dev','h5_37','246674','11350865','11350877'],#手机绑定+白名单测试
        'phone_reward':{'coin':200,'gold':20000},
##################################
	'is_test':0,  #客户端账号登录 0不开启调试账号 1强制开启调试账号 -1禁用调试账号（浏览器参数无效）    浏览器参数可参考index.html?test=1&testShow=1&testMsg=1
	'test_login':0,  #服务端端账号登录 0禁用uid登录 1可用uid登录    线上为0，需要调试时开为1
        'is_fast_register':1,#登录时直接快速注册
###################################
	'is_map_test':0,
	'is_guide':1, # 是否显示引导 0为关  新手引导
	'is_note':1, # 0不显示公告，1强制弹公告，2不强制弹公告
	'is_week_card':1, # 是否显示周卡
###################################
	'server_time_zone':8,    #时区（东八区），所有和server时间对比的new Date都应该以此为偏移生成时间戳
	'deviation':300,    #日偏移（分钟），只能是正数
###################################
        'affiche_url':'http://192.168.1.66/release/web66/ad2/',#公告的广告图片地址

	'tired_point':[      #【收益百分比】，【战斗中文字提示】，【在线时长】，【前端文字提示】，【提示时间间隔】
	#           [1,'',1,'',1],  
	#           [1,'pupil1',2,'pupil1',1],  
	#           [0.5,'pupil2',3,'pupil2',1], 
	#           [0.5,'pupil3',4,'pupil3',1],
	#           [0,'pupil4',24*60,'pupil4',2],
	           [1,'',24*60,'',24*60],  
	           [1,'',24*60,'',24*60],  
	           [1,'',24*60,'',24*60], 
	           [1,'',24*60,'',24*60],
	           [1,'',24*60,'',24*60],
	          ],
	'tired_cd':30,       #单位分钟，玩家的离线累积时间达到该时间后疲劳值清零
	'forbid_new':3*24*60,       #分钟数，开服超过这个分钟数禁止玩家注册新号
        'forbid_new_pf':{'a':72*60},#分钟数，开服超过这个分钟数禁止玩家注册新号 == pf 获取配置，否则看 forbid_new
	'sever_new':24*60,       #分钟数，开服在这个分钟数内显示为新，超过为爆
	'sever_showtime':1*60,       #分钟数，开开服之前提前可让玩家看到入口的分钟数
         'sever_only':1,#==1隐藏掉进不去的服
	'gm_speak':[],
'pf_festival': [[], ['r2g_xm_ios','r2g_xm_ad']],  # 【pf可见】【pf不可见】
	'merge_time':[       #合区时间点，1,2,3,4区将在某个日期合区（当天5点开始，所有活动不可见） 

	],


	'merge_time_0':[       #合区时间点，1,2,3,4区将在某个日期合区（当天5点开始，所有活动不可见） 	
[["10972","10973"],datetime.datetime(2024,8,13,12,0),'merge_notice1',0.8],  #参与合服区，预计合服时间，合服公告内容，最多提前N天显示
	],

	'merge_time_1':[       #合区时间点，1,2,3,4区将在某个日期合区（当天5点开始，所有活动不可见）
[["h1_1806","h1_1807"],datetime.datetime(2024,7,30,12,0),'merge_notice2',0.8],  #参与合服区，预计合服时间，合服公告内容，最多提前N天显示  	
	],

	'merge_time_2':[       #合区时间点，1,2,3,4区将在某个日期合区（当天5点开始，所有活动不可见）
[["h2_1157","h2_1158"],datetime.datetime(2024,8,13,12,0),'merge_notice3',0.8],  #参与合服区，预计合服时间，合服公,告内容，最多提前N天显示  	
	],

        'merge_time_3':[       #合区时间点，1,2,3,4区将在某个日期合区（当天5点开始，所有活动不可见）
[["h3_641","h3_642"],datetime.datetime(2024,8,13,12,0),'merge_notice4',0.8],  #参与合服区，预计合服时间，合服公告内容，最多提前N天显示  	
	],

        'merge_time_4':[       #合区时间点，1,2,3,4区将在某个日期合区（当天5点开始，所有活动不可见）
[["h4_27"],datetime.datetime(2024,6,26,12,0),'merge_notice5',0.8],  #参与合服区，预计合服时间，合服公告内容，最多提前N天显示  	
	],

        'merge_time_5':[       #合区时间点，1,2,3,4区将在某个日期合区（当天5点开始，所有活动不可见）
[["h5_11"],datetime.datetime(2024,1,5,12,0),'merge_notice6',0.8],  #参与合服区，预计合服时间，合服公告内容，最多提前N天显示  	
	],

        'merge_time_6':[       #合区时间点，1,2,3,4区将在某个日期合区（当天5点开始，所有活动不可见）
[["h6_6"],datetime.datetime(2024,6,26,12,0),'merge_notice7',0.8],  #参与合服区，预计合服时间，合服公告内容，最多提前N天显示  	
	],
	
	'act_left':[#主界面左侧活动排序 'folder':默认是ui 
                {'type':'sp_army_box',  'merge_hide':1, 'name':'sp_army_box01',       'icon':'icon_activity_30','folder':'later'},#奇士碎片抽奖
                {'type':'equip_box',   'merge_hide':0, 'name':'502073',  'icon':'icon_activity_19'},	#轩辕铸宝
                {'type':'god_police',    'merge_hide':0, 'name':'gods13',   'icon':'icon_activity_gods'},	#神灵巡游
                {'type':'rool_pay',    'merge_hide':1, 'name':'act_rool_pay_name',  'icon':'icon_activity_12'},	#循环充值 
                {'type':'happy_buy',   'merge_hide':1, 'name':'happy_buy_name',  'icon':'icon_activity_10'},	#七日嘉年华
                {'type':'festival',    'merge_hide':0, 'name':'act_festival_name',  'icon':'icon_activity_13'},	#节日活动 
                {'type':'limit_recharge','merge_hide':0, 'name':'act_festival_name2',   'icon':'icon_activity_13_2'},	#节日活动2（限定充值） 
		{'type':'wonder_act',  'merge_hide':1, 'name':'wonder_act_name',  'icon':'icon_activity_1'},	#精彩活动
		{'type':'limittime_act','merge_hide':1, 'name':'limittime_act_name',  'icon':'time_limit_icon'},	#限时活动
		{'type':'duplicate_shop','merge_hide':1, 'name':'duplicate_shop_name','icon':'icon_duplicate_out','folder':'later'},	#跨服商店
                {'type':'auction',     'merge_hide':1, 'name':'auction_name',  'icon':'icon_activity_14'},	#拍卖
                {'type':'pay_rank',    'merge_hide':0, 'name':'pay_rank0',  'icon':'icon_activity_16'},	#消费榜
		{'type':'free_buy',    'merge_hide':1, 'name':'free_buy_name',    'icon':'icon_activity_4'},#,'ui':'btn_32'
		{'type':'independ_buy','merge_hide':1, 'name':'independ_buy_name','icon':'icon_activity_2'},
		{'type':'act_base_up', 'merge_hide':1, 'name':'act_base_up_name', 'icon':'icon_activity_3','ui':'btn_32'},	               #官邸升级
                {'type':'act_37',      'merge_hide':0, 'name':'act_37_name',  'icon':'icon_activity_pf373'},#37活动##
                {'type':'act_37_2',    'merge_hide':0, 'name':'act_37_name2',  'icon':'icon_activity_pf37'},#37活动##
		{'type':'pay_again',   'merge_hide':0, 'name':'pay_again', 'icon':'icon_pay_again','ui':'btn_32'},	#充值福利
                {'type':'limit_free',  'merge_hide':1, 'name':'limit_free_name',  'icon':'icon_activity_9'},#限时免单
                {'type':'surprise_gift','merge_hide':0, 'name':'surprise_01',  'icon':'icon_activity_20'},	#惊喜礼包
                {'type':'surprise_gift_new','merge_hide':0, 'name':'surprise_01',         'icon':'icon_activity_20'},	      #新惊喜礼包
                {'type':'big_shot',     'merge_hide':0, 'name':'big_shot_name',       'icon':'icon_activity_31'},	      #主公觉醒

		{'type':'levelup_gift',  'merge_hide':0, 'name':'act_base_upgift_name',    'icon':'icon_activity_21','ui':'btn_32'},#官邸升级2
                {'type':'share_reward','merge_hide':0, 'name':'share_reward_name',  'icon':'icon_activity_8'},#微信分享
                {'type':'phone',       'merge_hide':0, 'name':'phone_name',  'icon':'icon_activity_11'},#手机注册
                #{'type':'focus',       'merge_hide':0, 'name':'focus_name',  'icon':'icon_focus'},#公众号关注
                #{'type':'verify',      'merge_hide':0, 'name':'verify_name',  'icon':'icon_verify'},#实名认证
	],
	'left_cfg':{
            'max_num': 7, #控制主界面左侧图标单列数量
            'name_lines': 1 #控制单个图标名字的行数（控制间距）
        },
        'pf_festival': [[], []],  # 【pf可见】【pf不可见】
       'third_ban':['h5_qq','h5_twyx2'],#第三方 屏蔽字接口 是否启用,包含pf的才使用
	'wonder_act':[				#精彩活动横向排序
{'type':'touhu',     'name':'touhu_name',       'icon':'icon_touhu',     'ui':'btn_32'},                     #投壶
		{'type':'act_wish','name':'act_wish_name','icon':'icon_shop06','ui':'btn_32'},                             #许愿
		{'type':'optional_ones','name':'optional_ones_name',  'icon':'icon_shop25',     'ui':'btn_32'},                     #私人订制
		{'type':'addup','name':'act_pay_addup_name','icon':'icon_shop03','ui':'btn_32', 'timeType': 'days'},       #累计充值
		{'type':'addup','name':'act_pay_addup_name','icon':'icon_shop03','ui':'btn_32', 'timeType': 'date'},       #累计充值
		{'type':'once','name':'act_pay_once_name','icon':'icon_shop04','ui':'btn_32', 'timeType': 'days'},	   #单笔充值
		{'type':'once','name':'act_pay_once_name','icon':'icon_shop04','ui':'btn_32', 'timeType': 'date'},	   #单笔充值
		{'type':'member_card','name':'act_member_card_name','icon':'icon_shop23','ui':'btn_32'},	       #永久卡活动

		{'type':'week_card','name':'act_week_card_name','icon':'icon_activity_6','ui':'btn_32'},	       #周卡活动
		{'type':'act_officeup','name':'act_officeup_name','icon':'icon_shop11','ui':'btn_32'},                 #加官进爵
		{'type':'fund','name':'act_fund_name','icon':'icon_activity_5','ui':'btn_32'},	                       #超级基金
		{'type':'consume','name':'502054','icon':'icon_shop09','ui':'btn_32'},	               #累计消费
		{'type':'pay_skincoin','name':'502083','icon':'icon_shop24','ui':'btn_32'},	                           #购买皮肤币
		{'type':'permanent_total_pay','name':'502070','icon':'icon_permanent_pay','ui':'btn_32'},                  #永久累充
	],
	'limittime_act':[				#限时活动横向排序
                          
                {'type':'dial','name':'502055','icon':'icon_shop20','ui':'btn_32'},	               #三国密藏
                {'type':'treasure','name':'502057','icon':'icon_shop21','ui':'btn_32'},	               #天下珍宝
		{'type':'act_sale_shop','name':'act_sale_name','icon':'icon_activity_7','ui':'btn_32'},#折扣商店
                {'type':'exchange_shop','name':'502061','icon':'exchange_shop_icon','ui':'btn_32'},	               #兑换商店
                {'type':'pay_choose','name':'502062','icon':'pay_choose_icon','ui':'btn_32'},	               #充值自选
	],
#	'notice':[
#		{
#			'id':1,
#			'image':'bg_15.png',
#			'name':'notice_name1',
#			'info':'notice_info1',
#			'dateshow':'201289'
#		},
#		{
#			'id':2,
#			'name':'200030',
#			'info':'201240',
#			'dateshow':'201289'
#		},
#	],
         'pay':{#第三方支付配置,对应平台pf,
                 'ios':{
                           'pay0':'com.djcy.game.sg.p1',
                           'pay1':'com.djcy.game.sg.p1',
                           'pay2':'com.djcy.game.sg.p2',
                           'pay3':'com.djcy.game.sg.p3',
                           'pay4':'com.djcy.game.sg.p4',
                           'pay5':'com.djcy.game.sg.p5',
                           'pay6':'com.djcy.game.sg.p6',
                           'pay7':'com.djcy.game.sg.p7',
                           'pay8':'com.djcy.game.sg.p8',
                           'pay101':'com.djcy.game.sg.p101',
                           'pay102':'com.djcy.game.sg.p102',
                           'pay103':'com.djcy.game.sg.p103',
                           'pay104':'com.djcy.game.sg.p104',
                           'pay105':'com.djcy.game.sg.p105',
                           'pay219':'com.djcy.game.sg.p219',
                           'pay201':'com.djcy.game.sg.p201',
                           'pay202':'com.djcy.game.sg.p202',
                           'pay203':'com.djcy.game.sg.p203',
                           'pay204':'com.djcy.game.sg.p204',
                           'pay205':'com.djcy.game.sg.p205',
                           'pay206':'com.djcy.game.sg.p206',
                           'pay207':'com.djcy.game.sg.p207',
                           'pay208':'com.djcy.game.sg.p208',
                           'pay209':'com.djcy.game.sg.p209',
                           'pay210':'com.djcy.game.sg.p210',
                           'pay211':'com.djcy.game.sg.p211',
                           'pay212':'com.djcy.game.sg.p212',
                           'pay213':'com.djcy.game.sg.p213',
'pay301':'com.djcy.game.sg.p1',
'pay302':'com.djcy.game.sg.p2',
'pay303':'com.djcy.game.sg.p3',
'pay304':'com.djcy.game.sg.p4',
'pay305':'com.djcy.game.sg.p5',
'pay306':'com.djcy.game.sg.p6',
'pay401':'com.djcy.game.sg.p205',
'pay402':'com.djcy.game.sg.p7',
                  },
                 'ios_djcy':{
                           'pay0':'com.djcy.game.sg.p1',
                           'pay1':'com.djcy.game.sg.p1',
                           'pay2':'com.djcy.game.sg.p2',
                           'pay3':'com.djcy.game.sg.p3',
                           'pay4':'com.djcy.game.sg.p4',
                           'pay5':'com.djcy.game.sg.p5',
                           'pay6':'com.djcy.game.sg.p6',
                           'pay7':'com.djcy.game.sg.p7',
                           'pay8':'com.djcy.game.sg.p8',
                           'pay101':'com.djcy.game.sg.p101',
                           'pay102':'com.djcy.game.sg.p102',
                           'pay103':'com.djcy.game.sg.p103',
                           'pay104':'com.djcy.game.sg.p104',
                           'pay105':'com.djcy.game.sg.p105',
                           'pay219':'com.djcy.game.sg.p219',
                           'pay201':'com.djcy.game.sg.p201',
                           'pay202':'com.djcy.game.sg.p202',
                           'pay203':'com.djcy.game.sg.p203',
                           'pay204':'com.djcy.game.sg.p204',
                           'pay205':'com.djcy.game.sg.p205',
                           'pay206':'com.djcy.game.sg.p206',
                           'pay207':'com.djcy.game.sg.p207',
                           'pay208':'com.djcy.game.sg.p208',
                           'pay209':'com.djcy.game.sg.p209',
                           'pay210':'com.djcy.game.sg.p210',
                           'pay211':'com.djcy.game.sg.p211',
                           'pay212':'com.djcy.game.sg.p212',
                           'pay213':'com.djcy.game.sg.p213',
'pay301':'com.djcy.game.sg.p1',
'pay302':'com.djcy.game.sg.p2',
'pay303':'com.djcy.game.sg.p3',
'pay304':'com.djcy.game.sg.p4',
'pay305':'com.djcy.game.sg.p5',
'pay306':'com.djcy.game.sg.p6',
'pay401':'com.djcy.game.sg.p205',
'pay402':'com.djcy.game.sg.p7',
                  },
                 'ios_sh':{
                           'pay0':'com.djcy.game.sg.p1',
                           'pay1':'com.djcy.game.sg.p1',
                           'pay2':'com.djcy.game.sg.p2',
                           'pay3':'com.djcy.game.sg.p3',
                           'pay4':'com.djcy.game.sg.p4',
                           'pay5':'com.djcy.game.sg.p5',
                           'pay6':'com.djcy.game.sg.p6',
                           'pay7':'com.djcy.game.sg.p7',
                           'pay8':'com.djcy.game.sg.p8',
                           'pay101':'com.djcy.game.sg.p101',
                           'pay102':'com.djcy.game.sg.p102',
                           'pay103':'com.djcy.game.sg.p103',
                           'pay104':'com.djcy.game.sg.p104',
                           'pay105':'com.djcy.game.sg.p105',
                           'pay219':'com.djcy.game.sg.p219',
                           'pay201':'com.djcy.game.sg.p201',
                           'pay202':'com.djcy.game.sg.p202',
                           'pay203':'com.djcy.game.sg.p203',
                           'pay204':'com.djcy.game.sg.p204',
                           'pay205':'com.djcy.game.sg.p205',
                           'pay206':'com.djcy.game.sg.p206',
                           'pay207':'com.djcy.game.sg.p207',
                           'pay208':'com.djcy.game.sg.p208',
                           'pay209':'com.djcy.game.sg.p209',
                           'pay210':'com.djcy.game.sg.p210',
                           'pay211':'com.djcy.game.sg.p211',
                           'pay212':'com.djcy.game.sg.p212',
                           'pay213':'com.djcy.game.sg.p213',
'pay301':'com.djcy.game.sg.p1',
'pay302':'com.djcy.game.sg.p2',
'pay303':'com.djcy.game.sg.p3',
'pay304':'com.djcy.game.sg.p4',
'pay305':'com.djcy.game.sg.p5',
'pay306':'com.djcy.game.sg.p6',
'pay401':'com.djcy.game.sg.p205',
'pay402':'com.djcy.game.sg.p7',
                  },
                  'h5_wende':{
                          'pay1':'com.sgz.xbtx_6',
                          'pay2':'com.sgz.xbtx_30',
                          'pay3':'com.sgz.xbtx_68',
	                  'pay4':'com.sgz.xbtx_128',
	                  'pay5':'com.sgz.xbtx_328',
	                  'pay6':'com.sgz.xbtx_648',
	                  'pay7':'com.sgz.xbtx_pay7',
	                  'pay8':'com.sgz.xbtx_pay8',
	                  'pay101':'com.sgz.xbtx_pay101',
	                  'pay102':'com.sgz.xbtx_pay102',
	                  'pay103':'com.sgz.xbtx_pay103',
	                  'pay104':'com.sgz.xbtx_pay104',
	                  'pay105':'com.sgz.xbtx_pay105',
	                  'pay201':'com.sgz.xbtx_pay201',
	                  'pay202':'com.sgz.xbtx_pay202',
	                  'pay203':'com.sgz.xbtx_pay203',
	                  'pay204':'com.sgz.xbtx_pay204',
	                  'pay205':'com.sgz.xbtx_pay205',
	                  'pay206':'com.sgz.xbtx_pay206',
	                  'pay207':'com.sgz.xbtx_pay207',
	                  'pay208':'com.sgz.xbtx_pay208',
	                  'pay209':'com.sgz.xbtx_pay209',
	                  'pay210':'com.sgz.xbtx_pay210',
	                  'pay211':'com.sgz.xbtx_pay211',
	                  'pay212':'com.sgz.xbtx_pay212',
	                  'pay213':'com.sgz.xbtx_pay213',
	                  'pay214':'com.sgz.xbtx_pay214',
	                  'pay215':'com.sgz.xbtx_pay215',
	                  'pay216':'com.sgz.xbtx_pay216',
	                  'pay217':'com.sgz.xbtx_pay217',
	                  'pay218':'com.sgz.xbtx_pay218',
	                  'pay219':'com.sgz.xbtx_pay219',
'pay301':'com.sgz.xbtx_6',
'pay302':'com.sgz.xbtx_30',
'pay303':'com.sgz.xbtx_68',
'pay304':'com.sgz.xbtx_128',
'pay305':'com.sgz.xbtx_328',
'pay306':'com.sgz.xbtx_648',
'pay401':'com.sgz.xbtx_pay205',
'pay402':'com.sgz.xbtx_pay7',
                   },
         },
         'pf_change_zone':{#根据区换平台pf
                'juedi_ad':[],
         },
         'pay_money':{
                'google':{
                           'pay0':'1',
                           'pay1':'1',
                           'pay2':'5',
                           'pay3':'11',
                           'pay4':'21',
                           'pay5':'54',
                           'pay6':'108',
                           'pay7':'166',
                           'pay8':'332'
                },
         },
         'fix':{
                  'default':'bug_fix_02',
                  #'hw':'bug_fix_02',

         },
    "duplicate_groups": [          #跨服赛分组！！！！！！！！！！！！！！！   index范围0~9999
        {  
            "id": "test1",   
            "index": 250,              
            "name": unicode("test1", "utf-8"),   
            "zone_range":[                       #区号范围前后都包含
               [],
               [],  #h1       [[1,999],[10000,11999]]
               [],  #h2
               [],  #h3
               [],  #h4
               [],  #h5
               [],  #h6
               [],  #h7
            ],
            "open": -1,                           #开启的最高报名段位-1不开跨服0青铜1白银2黄金3闪耀4最强
            "shop_open": 0,                      #跨服商店1开启 0关闭 
            "zone_groups": [],           #可见zone_groups权限
        },
        {  
            "id": "test2",   
            "index": 251,              
            "name": unicode("test2", "utf-8"),   
            "zone_range":[                       #区号范围前后都包含
               [],
               [],  #h1
               [],  #h2
               [],  #h3
               [],  #h4
               [],  #h5
               [],  #h6
               [],  #h7

            ],
            "open": -1,                           #开启的最高报名段位-1不开跨服0青铜1白银2黄金3闪耀4最强
            "shop_open": 0,                      #跨服商店1开启 0关闭 
            "zone_groups": [],           #可见zone_groups权限
        },
        {  
            "id": "main",   
            "index": 100,              
            "name": unicode("自营", "utf-8"),    
            "zone_range":[                       #区号范围前后都包含
               [],
               [[1,499]],  #h1
               [[1,499]],  #h2
               [[1,499]],  #h3
               [[1,499]],  #h4
               [[1,499]],  #h5
               [[1,499]],  #h6
               [[1,499]],  #h7
            ],
            "open": 3,                           #开启的最高报名段位-1不开跨服0青铜1白银2黄金3闪耀4最强
            "shop_open": 1,                      #跨服商店1开启 0关闭 
            "zone_groups": ["main"],           #可见zone_groups权限
        },
        {  
            "id": "37game",   
            "index": 101,  
            "name": unicode("37game", "utf-8"),
            "zone_range":[                       #区号范围前后都包含
               [],
               [[501,699],[1001,2999]],  #h1
               [[501,699],[1001,2999]],  #h2
               [[501,699],[1001,2999]],  #h3
               [[501,699],[1001,2999]],  #h4
               [[501,699],[1001,2999]],  #h5
               [[501,699],[1001,2999]],  #h6
               [[501,699],[1001,2999]],  #h7
            ],
            "open": 3,                           #开启的最高报名段位-1不开跨服0青铜1白银2黄金3闪耀4最强
            "shop_open": 1,                      #跨服商店1开启 0关闭 
            "zone_groups": ["37game"],           #可见zone_groups权限
        },
        {  
            "id": "tanwan",   
            "index": 102,  
            "name": unicode("tanwan", "utf-8"),
            "zone_range":[                       #区号范围前后都包含
               [],
               [[701,999],[3000,4999]],    #h1
               [[701,999],[3000,4999]],    #h2
               [[701,999],[3000,4999]],    #h3
               [[701,999],[3000,4999]],    #h4
               [[701,999],[3000,4999]],    #h5
               [[701,999],[3000,4999]],    #h6
               [[701,999],[3000,4999]],    #h7
            ],
            "open": 3,                           #开启的最高报名段位-1不开跨服0青铜1白银2黄金3闪耀4最强
            "shop_open": 1,                      #跨服商店1开启 0关闭 
            "zone_groups": ["tanwan"],           #可见zone_groups权限
        },
        {  
            "id": "main2",   
            "index": 200,              
            "name": unicode("自营2", "utf-8"),    
            "zone_range":[                       #区号范围前后都包含
               [],
               [],  #h1
               [],  #h2
               [],  #h3
               [],  #h4
               [],  #h5
               [],  #h6
               [],  #h7
            ],
            "open": -1,                           #开启的最高报名段位-1不开跨服0青铜1白银2黄金3闪耀4最强
            "shop_open": 1,                      #跨服商店1开启 0关闭 
            "zone_groups": ["main"],           #可见zone_groups权限
        },
        {  
            "id": "37game2",   
            "index": 201,  
            "name": unicode("37game2", "utf-8"),
            "zone_range":[                       #区号范围前后都包含
               [],
               [],  #h1
               [],  #h2
               [],  #h3
               [],  #h4
               [],  #h5
               [],  #h6
               [],  #h7
            ],
            "open": -1,                           #开启的最高报名段位-1不开跨服0青铜1白银2黄金3闪耀4最强
            "shop_open": 1,                      #跨服商店1开启 0关闭 
            "zone_groups": ["37game"],           #可见zone_groups权限
        },
        { #最后一位默认没有zone_range关键字，表示剩余所有区
            "id": "default",   
            "index": 0,  
            "name": unicode("混合", "utf-8"),        #混合大区
            "open": -1,                           #开启的最高报名段位-1不开跨服0青铜1白银2黄金3闪耀4最强
            "shop_open": 0,                      #跨服商店1开启 0关闭 
            "zone_groups": [],                 #可见zone_groups权限
        },
    ],

##### 预注册奖励配置
    'pre_register_reward':[  ## 邮件标题,邮件内容,奖励PF白名单，奖励内容
         'pre_register_name',
         'pre_register_info',
         ['ea37', 'tw37'],
         {'coin':200,'gold':20000}
    ],
#### 额外黄金返利开关（37）
    'ext_coin_pfs': ['ad_37_kr', 'ad_37_jp'],  ## 在列表中的pf才会给额外黄金返利
#### 赤壁37 pf对应key和game_key
    'pf_game_key': {
    },
#### 37实名认证礼包
    'real_auth_reward': {'coin':200,'gold':20000},
#### 充值回调IP白名单
    'pay_callback_whitelist': {
        'cn37': [                 #国内37
            '************',
            '************',
            '*************',
            '*************',
            '*************',
            '*************',
            '*************',
            '**************',
            '*************',
            '***********',
            '************',
            '************',
            '*************',
            '*************',
            '**************',
            '**************',
            '**************',
            '**************',
            '**************',
            '**************',
            '*************',
            '*************',
            '************',
            '************** ',
            '***************',
            '***************',
            '*************',
            '************',
            '************',
            '**************',
            '**************',
            '42.194.233.254',
            '42.194.230.180',
            '42.194.226.92',
            '221.5.32.10',
            '221.5.32.59',
            '221.5.32.166',
            '221.5.32.168',
            '221.5.32.186',
            '221.5.32.188',
            '221.5.32.118',
            '221.5.110.26',
            '221.5.32.199',
            '221.5.32.7',
            '221.5.32.29',
            '221.5.32.28',
            '120.77.217.227',
            '119.23.255.57',
            '112.74.168.156',
            '119.23.216.132',
            '112.74.36.37',
            '43.139.48.19',
            '43.136.101.221',
            '43.139.90.221',
            '43.139.104.31',
            '119.91.239.197',



        ],
    },
}