package sg.activities.view
{
    import laya.events.Event;

    import sg.activities.model.ModelActivities;
    import sg.activities.model.ModelPermanentPayTotal;
    import sg.boundFor.GotoManager;
    import sg.manager.ViewManager;
    import sg.net.NetMethodCfg;
    import sg.utils.ObjectUtil;
    import sg.utils.Tools;

    import ui.activities.payTotal.payTotalUI;
    import sg.manager.AssetsManager;
    import sg.utils.TimeHelper;
    import sg.model.ModelUser;
    import sg.cfg.ConfigApp;
    import laya.ui.Button;
    import sg.view.emboitement.ViewEmboitement;
    import sg.cfg.Platform;

    /**
     * 永久累计充值主界面 - 完全复刻PayTotalMain，但显示代金券且永久有效
     */
    public class PermanentPayTotalMain extends payTotalUI
    {
        private var model:ModelPermanentPayTotal = ModelPermanentPayTotal.instance;
        private var rewardData:Object;
        private var pay_list:Array;
        private var reward_list:Array;
        private var totalPay:int;
        
        public function PermanentPayTotalMain()
        {
            this.showList.scrollBar.hide = true;
            this.showList.itemRender = ActRewardListBase;
            this.model.on(ModelActivities.UPDATE_DATA, this, this.refreshPanel);
            this.on(Event.DISPLAY, this, this._onDisplay);
            this.btn_pay.label = Tools.getMsgById('_public104');
            this.btn_pay.on(Event.CLICK, this, GotoManager.boundForPanel, [GotoManager.VIEW_PAY_TEST]);
            if(this["btnTips"]){
                (this["btnTips"] as Button).on(Event.CLICK,this,function():void{
                    ViewManager.instance.showTipsPanel("永久累计充值代金券，达到指定金额即可领取奖励，永久有效！");
                });
            }
        }

        private function _onDisplay():void
        {
            this.refreshPanel();
            this.refreshtime();
            Laya.timer.loop(1000, this, this.refreshtime);
        }

        private function refreshtime():void
        {
            this.timeTxt.text = this.model.getTimeString();
        }

        private function refreshPanel():void {
            // 获取配置
            var cfg:Object = this.model.getConfig();
            if (!cfg) return;
            
            this.rewardData = cfg['reward'];
            var data:Object = this.model.getData();
            if (!data) return;
            
            this.pay_list = data['pay_list'];
            this.reward_list = data['reward_list'];
            this.totalPay = 0;
            var len:int = this.pay_list.length;
            for(var i:int = 0; i < len; i++)
            {
                this.totalPay += this.pay_list[i];
            }
            
            // 显示累计充值代金券（这里是关键修改点）
            this.payIcon.setData(AssetsManager.getAssetsUI(AssetsManager.IMG_COIN), this.totalPay + "代金券");
            
            // 设置角色图标
            if (cfg['character']) {
                this.character.setHeroIcon(cfg['character']);
            }

            // 套装按钮
            btn_suit.visible = false;
            if (cfg['show']) {
                btn_suit.visible = true;
                var suitName:String = Tools.getMsgById(cfg['name']);
                btn_suit.label = Tools.getMsgById('_jia0088', [suitName]);
                btn_suit.on(Event.CLICK, ViewManager.instance, ViewManager.instance.showView, [["ViewEmboitement",ViewEmboitement],[cfg['show'], cfg['info'], suitName]]);
            }
            Tools.textFitFontSize(this.btn_suit);
            
            var keys:Array = ObjectUtil.keys(cfg['reward']);
            var dataArr:Array = [];
            for (i = 0, len = keys.length; i < len; ++i) {
                var key:String = keys[i];
                var reward:Object = this.rewardData[key];
                var flag:int = 0;
                var needMoney:int = parseInt(key);
                this.totalPay >= needMoney && (flag = 1);
                this.reward_list.indexOf(needMoney) !== -1 && (flag = 2);
                var alreadyPay:int = flag ? needMoney : 0;
                var canShow:Boolean = true;
                if (cfg.reward_show && cfg.reward_show[key]) {
                    canShow = totalPay >= cfg.reward_show[key];
                }
                canShow && dataArr.push({
                    'view':this,
                    'currentNum': this.totalPay + "代金券", // 显示代金券
                    'needNum': needMoney + "代金券", // 显示代金券
                    'reward_key':needMoney,//传给服务器的参数
                    'reward':reward,
                    'flag':flag,
                    'imgUrl':'actPay3_15.png'
                });
            }
            dataArr.sort(function (a:Object, b:Object):Number { 
                return parseInt(a.needNum) - parseInt(b.needNum); 
            });
            var tempArr1:Array = dataArr.filter(function (a:Object):Boolean { return a.flag === 1; });
            var tempArr2:Array = dataArr.filter(function (a:Object):Boolean { return a.flag === 0; });
            var tempArr3:Array = dataArr.filter(function (a:Object):Boolean { return a.flag === 2; });
            this.showList.array = tempArr1.concat(tempArr2, tempArr3);
        }

        public function getReward(key:int):void
        {
            Platform.uploadActionData(8, [key]);
            // 调用永久累计充值的领取接口
            ModelActivities.instance.sendMethod(NetMethodCfg.WS_SR_GET_PERMANENT_PAY_REWARD, {reward_money: key});
        }

        public function removeCostumeEvent():void 
        {
            Laya.timer.clear(this, this.refreshtime);
            this.model.off(ModelActivities.UPDATE_DATA, this, this.refreshPanel);
        }
    }
}
